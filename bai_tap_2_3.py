#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bài tập 2.3: <PERSON><PERSON> thống quản lý siêu thị
Tạo mô hình E-R mở rộng và chuyển đổi về mô hình E-R kinh điển
"""

from graphviz import Digraph

def create_erd_mo_rong():
    """Tạo mô hình E-R mở rộng cho hệ thống siêu thị"""
    dot = Digraph(comment='Mô hình E-R Mở rộng - Hệ thống Siêu thị')
    dot.attr(rankdir='TB', size='20,16', dpi='300')
    dot.attr('node', fontname='Arial', fontsize='9')
    dot.attr('edge', fontname='Arial', fontsize='8')
    
    # === THỰC THỂ CHÍNH ===
    # Nhà cung cấp
    dot.node('NHA_CUNG_CAP', 
             'NHA_CUNG_CAP\\n<U>So_hieu_NCC</U>\\nTen_NCC\\nDia_chi\\nSo_dien_thoai',
             shape='box', style='filled', fillcolor='lightblue')
    
    # Mặt hàng
    dot.node('MAT_HANG',
             'MAT_HANG\\n<U>Ma_hang</U>\\nTen_hang\\nMau_sac\\nTrong_luong\\nDon_vi_tinh\\nSo_luong_ton',
             shape='box', style='filled', fillcolor='lightblue')
    
    # Gian hàng
    dot.node('GIAN_HANG',
             'GIAN_HANG\\n<U>Ma_gian_hang</U>\\nTen_gian_hang',
             shape='box', style='filled', fillcolor='lightblue')
    
    # Nhân viên
    dot.node('NHAN_VIEN',
             'NHAN_VIEN\\n<U>Ma_nhan_vien</U>\\nHo_va_ten\\nLuong',
             shape='box', style='filled', fillcolor='lightblue')
    
    # Khách hàng
    dot.node('KHACH_HANG',
             'KHACH_HANG\\n<U>Ma_khach_hang</U>\\nHo_va_ten\\nDia_chi',
             shape='box', style='filled', fillcolor='lightblue')
    
    # Thân nhân
    dot.node('THAN_NHAN',
             'THAN_NHAN\\n<U>Ma_nhan_vien</U>\\n<U>Ma_than_nhan</U>\\nHo_ten\\nNgay_sinh\\nGioi_tinh\\nQuan_he',
             shape='box', style='filled', fillcolor='lightblue')
    
    # === QUAN HỆ NHIỀU-NHIỀU (M:N) ===
    # Quan hệ cung cấp (NCC - Mặt hàng)
    dot.node('CUNG_CAP',
             'CUNG_CAP\\nDon_gia\\nSo_luong_cung_cap\\nNgay_cung_cap',
             shape='diamond', style='filled', fillcolor='lightgreen')
    
    # Quan hệ bán tại (Mặt hàng - Gian hàng)
    dot.node('BAN_TAI',
             'BAN_TAI',
             shape='diamond', style='filled', fillcolor='lightgreen')
    
    # Quan hệ tư vấn (Nhân viên - Gian hàng)
    dot.node('TU_VAN',
             'TU_VAN',
             shape='diamond', style='filled', fillcolor='lightgreen')
    
    # Quan hệ mua hàng (Khách hàng - Mặt hàng)
    dot.node('MUA_HANG',
             'MUA_HANG\\nMa_hoa_don\\nNgay_mua\\nSo_luong\\nDon_gia_ban',
             shape='diamond', style='filled', fillcolor='lightgreen')
    
    # Quan hệ có thân nhân (Nhân viên - Thân nhân)
    dot.node('CO_THAN_NHAN',
             'CO_THAN_NHAN',
             shape='diamond', style='filled', fillcolor='lightgreen')
    
    # === CÁC LIÊN KẾT ===
    # NCC cung cấp mặt hàng (M:N)
    dot.edge('NHA_CUNG_CAP', 'CUNG_CAP', label='1..n')
    dot.edge('CUNG_CAP', 'MAT_HANG', label='1..n')
    
    # Mặt hàng bán tại gian hàng (N:1)
    dot.edge('MAT_HANG', 'BAN_TAI', label='1..n')
    dot.edge('BAN_TAI', 'GIAN_HANG', label='1..1')
    
    # Nhân viên tư vấn gian hàng (N:M)
    dot.edge('NHAN_VIEN', 'TU_VAN', label='1..n')
    dot.edge('TU_VAN', 'GIAN_HANG', label='1..n')
    
    # Khách hàng mua mặt hàng (M:N)
    dot.edge('KHACH_HANG', 'MUA_HANG', label='1..n')
    dot.edge('MUA_HANG', 'MAT_HANG', label='1..n')
    
    # Nhân viên có thân nhân (1:N)
    dot.edge('NHAN_VIEN', 'CO_THAN_NHAN', label='1..1')
    dot.edge('CO_THAN_NHAN', 'THAN_NHAN', label='1..n')
    
    return dot

def create_erd_kinh_dien():
    """Chuyển đổi mô hình E-R mở rộng về mô hình E-R kinh điển"""
    dot = Digraph(comment='Mô hình E-R Kinh điển - Hệ thống Siêu thị')
    dot.attr(rankdir='TB', size='24,18', dpi='300')
    dot.attr('node', fontname='Arial', fontsize='9')
    dot.attr('edge', fontname='Arial', fontsize='8')
    
    # === THỰC THỂ GỐC ===
    dot.node('NHA_CUNG_CAP', 
             'NHA_CUNG_CAP\\n<U>So_hieu_NCC</U>\\nTen_NCC\\nDia_chi\\nSo_dien_thoai',
             shape='box', style='filled', fillcolor='lightblue')
    
    dot.node('MAT_HANG',
             'MAT_HANG\\n<U>Ma_hang</U>\\nTen_hang\\nMau_sac\\nTrong_luong\\nDon_vi_tinh\\nSo_luong_ton',
             shape='box', style='filled', fillcolor='lightblue')
    
    dot.node('GIAN_HANG',
             'GIAN_HANG\\n<U>Ma_gian_hang</U>\\nTen_gian_hang',
             shape='box', style='filled', fillcolor='lightblue')
    
    dot.node('NHAN_VIEN',
             'NHAN_VIEN\\n<U>Ma_nhan_vien</U>\\nHo_va_ten\\nLuong',
             shape='box', style='filled', fillcolor='lightblue')
    
    dot.node('KHACH_HANG',
             'KHACH_HANG\\n<U>Ma_khach_hang</U>\\nHo_va_ten\\nDia_chi',
             shape='box', style='filled', fillcolor='lightblue')
    
    dot.node('THAN_NHAN',
             'THAN_NHAN\\n<U>Ma_nhan_vien</U>\\n<U>Ma_than_nhan</U>\\nHo_ten\\nNgay_sinh\\nGioi_tinh\\nQuan_he',
             shape='box', style='filled', fillcolor='lightblue')
    
    # === THỰC THỂ MỚI (từ quan hệ M:N) ===
    # Từ quan hệ CUNG_CAP
    dot.node('CHI_TIET_CUNG_CAP',
             'CHI_TIET_CUNG_CAP\\n<U>So_hieu_NCC</U>\\n<U>Ma_hang</U>\\nDon_gia\\nSo_luong_cung_cap\\nNgay_cung_cap',
             shape='box', style='filled', fillcolor='lightyellow')
    
    # Từ quan hệ MUA_HANG
    dot.node('HOA_DON_BAN',
             'HOA_DON_BAN\\n<U>Ma_hoa_don</U>\\nMa_khach_hang\\nNgay_mua',
             shape='box', style='filled', fillcolor='lightyellow')
    
    dot.node('CHI_TIET_HOA_DON',
             'CHI_TIET_HOA_DON\\n<U>Ma_hoa_don</U>\\n<U>Ma_hang</U>\\nSo_luong\\nDon_gia_ban',
             shape='box', style='filled', fillcolor='lightyellow')
    
    # Từ quan hệ TU_VAN
    dot.node('PHAN_CONG',
             'PHAN_CONG\\n<U>Ma_nhan_vien</U>\\n<U>Ma_gian_hang</U>\\nNgay_bat_dau\\nNgay_ket_thuc',
             shape='box', style='filled', fillcolor='lightyellow')
    
    # === QUAN HỆ 1:1 và 1:N ===
    dot.node('cung_cap', 'cung_cap', shape='diamond', style='filled', fillcolor='lightgreen')
    dot.node('ban_tai', 'ban_tai', shape='diamond', style='filled', fillcolor='lightgreen')
    dot.node('lap_hd', 'lap_hd', shape='diamond', style='filled', fillcolor='lightgreen')
    dot.node('chi_tiet', 'chi_tiet', shape='diamond', style='filled', fillcolor='lightgreen')
    dot.node('phan_cong', 'phan_cong', shape='diamond', style='filled', fillcolor='lightgreen')
    dot.node('co_than_nhan', 'co_than_nhan', shape='diamond', style='filled', fillcolor='lightgreen')
    
    # === CÁC LIÊN KẾT ===
    # NCC -> CHI_TIET_CUNG_CAP (1:N)
    dot.edge('NHA_CUNG_CAP', 'cung_cap', label='1..1')
    dot.edge('cung_cap', 'CHI_TIET_CUNG_CAP', label='1..n')
    
    # MAT_HANG -> CHI_TIET_CUNG_CAP (1:N)
    dot.edge('MAT_HANG', 'CHI_TIET_CUNG_CAP', label='1..n')
    
    # MAT_HANG -> GIAN_HANG (N:1)
    dot.edge('MAT_HANG', 'ban_tai', label='1..n')
    dot.edge('ban_tai', 'GIAN_HANG', label='1..1')
    
    # KHACH_HANG -> HOA_DON_BAN (1:N)
    dot.edge('KHACH_HANG', 'lap_hd', label='1..1')
    dot.edge('lap_hd', 'HOA_DON_BAN', label='1..n')
    
    # HOA_DON_BAN -> CHI_TIET_HOA_DON (1:N)
    dot.edge('HOA_DON_BAN', 'chi_tiet', label='1..1')
    dot.edge('chi_tiet', 'CHI_TIET_HOA_DON', label='1..n')
    
    # MAT_HANG -> CHI_TIET_HOA_DON (1:N)
    dot.edge('MAT_HANG', 'CHI_TIET_HOA_DON', label='1..n')
    
    # NHAN_VIEN -> PHAN_CONG (1:N)
    dot.edge('NHAN_VIEN', 'phan_cong', label='1..1')
    dot.edge('phan_cong', 'PHAN_CONG', label='1..n')
    
    # GIAN_HANG -> PHAN_CONG (1:N)
    dot.edge('GIAN_HANG', 'PHAN_CONG', label='1..n')
    
    # NHAN_VIEN -> THAN_NHAN (1:N)
    dot.edge('NHAN_VIEN', 'co_than_nhan', label='1..1')
    dot.edge('co_than_nhan', 'THAN_NHAN', label='1..n')
    
    return dot

def main():
    """Tạo và lưu các mô hình E-R"""
    print("=== BÀI TẬP 2.3: HỆ THỐNG QUẢN LÝ SIÊU THỊ ===")
    print()
    
    print("1. Đang tạo mô hình E-R mở rộng...")
    erd_mo_rong = create_erd_mo_rong()
    erd_mo_rong.render('bai_2_3_mo_rong', format='png', cleanup=True)
    print("✓ Đã tạo bai_2_3_mo_rong.png")
    
    print("2. Đang tạo mô hình E-R kinh điển...")
    erd_kinh_dien = create_erd_kinh_dien()
    erd_kinh_dien.render('bai_2_3_kinh_dien', format='png', cleanup=True)
    print("✓ Đã tạo bai_2_3_kinh_dien.png")
    
    print()
    print("=== HOÀN THÀNH ===")
    print("Các file đã được tạo:")
    print("- bai_2_3_mo_rong.png (Mô hình E-R mở rộng)")
    print("- bai_2_3_kinh_dien.png (Mô hình E-R kinh điển)")
    print()
    print("Quá trình chuyển đổi:")
    print("• Quan hệ M:N CUNG_CAP → Thực thể CHI_TIET_CUNG_CAP")
    print("• Quan hệ M:N MUA_HANG → Thực thể HOA_DON_BAN + CHI_TIET_HOA_DON")
    print("• Quan hệ M:N TU_VAN → Thực thể PHAN_CONG")
    print("• Quan hệ 1:N được giữ nguyên")

if __name__ == "__main__":
    main()
