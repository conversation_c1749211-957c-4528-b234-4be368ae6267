@startuml
!define ENTITY(name,desc) entity name as "desc"
!define RELATIONSHIP(name,desc) diamond name as "desc"

' <PERSON><PERSON><PERSON> nghĩa các thực thể
ENTITY(NCC, "NHA_CUNG_CAP\n----\n<u>SoHieuNCC</u>\nTenNCC\nDiaChi\nSoDienThoai")
ENTITY(MH, "MAT_HANG\n----\n<u>MaHang</u>\nTenHang\nMauSac\nTrongLuong\nDonViTinh\nSoLuongTon")
ENTITY(GH, "GIAN_HANG\n----\n<u>MaGianHang</u>\nTenGianHang")
ENTITY(NV, "NHAN_VIEN\n----\n<u>MaNV</u>\nHoTen\nLuong")
ENTITY(KH, "KHACH_HANG\n----\n<u>MaKH</u>\nHoTen\nDia<PERSON>hi")
ENTITY(HDN, "HOA_DON_NHAP\n----\n<u>MaHDNhap</u>\nNgayNhap")
ENTITY(HDB, "HOA_DON_BAN\n----\n<u>MaHDBan</u>\nNgayBan")
ENTITY(TN, "THAN_NHAN\n----\n<u>MaThanNhan</u>\nHoTen\nNgaySinh\nGioiTinh\nQuanHe")

' Định nghĩa các quan hệ
RELATIONSHIP(R1, "CUNG_CAP\n----\nDonGia")
RELATIONSHIP(R2, "BAN_TAI")
RELATIONSHIP(R3, "TU_VAN")
RELATIONSHIP(R4, "NHAP_HANG")
RELATIONSHIP(R5, "MUA_HANG")
RELATIONSHIP(R6, "CT_HD_NHAP\n----\nSoLuong\nDonGia")
RELATIONSHIP(R7, "CT_HD_BAN\n----\nSoLuong\nDonGia")
RELATIONSHIP(R8, "THAN_NHAN_CUA")

' Kết nối các thực thể với quan hệ
NCC ||--o{ R1
R1 }o--|| MH

GH ||--o{ R2
R2 }o--|| MH

NV ||--|| R3
R3 ||--|| GH

NCC ||--o{ R4
R4 }o--|| HDN

KH ||--o{ R5
R5 }o--|| HDB

HDN ||--o{ R6
R6 }o--|| MH

HDB ||--o{ R7
R7 }o--|| MH

NV ||--o{ R8
R8 }o--|| TN

' Chú thích bản số
note top of R1 : M:N
note top of R2 : 1:N
note top of R3 : 1:1
note top of R4 : 1:N
note top of R5 : 1:N
note top of R6 : M:N
note top of R7 : M:N
note top of R8 : 1:N

@enduml