#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bài tập 3.3: Chuyển đổi mô hình E-R thành mô hình quan hệ và viết SQL
"""

from graphviz import Digraph

def print_relational_model():
    """In mô hình quan hệ được chuyển đổi từ mô hình E-R"""
    print("=" * 80)
    print("BÀI TẬP 3.3: CHUYỂN ĐỔI MÔ HÌNH E-R THÀNH MÔ HÌNH QUAN HỆ")
    print("=" * 80)
    print()
    
    print("📋 MÔ HÌNH QUAN HỆ (RELATIONAL MODEL):")
    print("-" * 50)
    
    relations = [
        {
            "name": "NHA_CUNG_CAP",
            "attributes": ["So_hieu_NCC", "Ten_NCC", "Dia_chi", "So_dien_thoai"],
            "primary_key": ["So_hieu_NCC"],
            "foreign_keys": []
        },
        {
            "name": "MAT_HANG", 
            "attributes": ["Ma_hang", "Ten_hang", "Mau_sac", "Trong_luong", "Don_vi_tinh", "So_luong_ton"],
            "primary_key": ["Ma_hang"],
            "foreign_keys": []
        },
        {
            "name": "GIAN_HANG",
            "attributes": ["Ma_gian_hang", "Ten_gian_hang"],
            "primary_key": ["Ma_gian_hang"], 
            "foreign_keys": []
        },
        {
            "name": "NHAN_VIEN",
            "attributes": ["Ma_nhan_vien", "Ho_va_ten", "Luong"],
            "primary_key": ["Ma_nhan_vien"],
            "foreign_keys": []
        },
        {
            "name": "KHACH_HANG",
            "attributes": ["Ma_khach_hang", "Ho_va_ten", "Dia_chi"],
            "primary_key": ["Ma_khach_hang"],
            "foreign_keys": []
        },
        {
            "name": "THAN_NHAN",
            "attributes": ["Ma_nhan_vien", "Ma_than_nhan", "Ho_ten", "Ngay_sinh", "Gioi_tinh", "Quan_he"],
            "primary_key": ["Ma_nhan_vien", "Ma_than_nhan"],
            "foreign_keys": [("Ma_nhan_vien", "NHAN_VIEN", "Ma_nhan_vien")]
        },
        {
            "name": "CHI_TIET_CUNG_CAP",
            "attributes": ["So_hieu_NCC", "Ma_hang", "Don_gia", "So_luong_cung_cap", "Ngay_cung_cap"],
            "primary_key": ["So_hieu_NCC", "Ma_hang"],
            "foreign_keys": [
                ("So_hieu_NCC", "NHA_CUNG_CAP", "So_hieu_NCC"),
                ("Ma_hang", "MAT_HANG", "Ma_hang")
            ]
        },
        {
            "name": "HOA_DON_BAN",
            "attributes": ["Ma_hoa_don", "Ma_khach_hang", "Ngay_mua"],
            "primary_key": ["Ma_hoa_don"],
            "foreign_keys": [("Ma_khach_hang", "KHACH_HANG", "Ma_khach_hang")]
        },
        {
            "name": "CHI_TIET_HOA_DON",
            "attributes": ["Ma_hoa_don", "Ma_hang", "So_luong", "Don_gia_ban"],
            "primary_key": ["Ma_hoa_don", "Ma_hang"],
            "foreign_keys": [
                ("Ma_hoa_don", "HOA_DON_BAN", "Ma_hoa_don"),
                ("Ma_hang", "MAT_HANG", "Ma_hang")
            ]
        },
        {
            "name": "PHAN_CONG_TU_VAN",
            "attributes": ["Ma_nhan_vien", "Ma_gian_hang", "Ngay_bat_dau", "Ngay_ket_thuc"],
            "primary_key": ["Ma_nhan_vien", "Ma_gian_hang"],
            "foreign_keys": [
                ("Ma_nhan_vien", "NHAN_VIEN", "Ma_nhan_vien"),
                ("Ma_gian_hang", "GIAN_HANG", "Ma_gian_hang")
            ]
        },
        {
            "name": "BAN_TAI_GIAN_HANG",
            "attributes": ["Ma_hang", "Ma_gian_hang"],
            "primary_key": ["Ma_hang"],
            "foreign_keys": [
                ("Ma_hang", "MAT_HANG", "Ma_hang"),
                ("Ma_gian_hang", "GIAN_HANG", "Ma_gian_hang")
            ]
        }
    ]
    
    for i, rel in enumerate(relations, 1):
        print(f"{i}. {rel['name']}({', '.join(rel['attributes'])})")
        print(f"   • Primary Key: {{{', '.join(rel['primary_key'])}}}")
        if rel['foreign_keys']:
            print("   • Foreign Keys:")
            for fk in rel['foreign_keys']:
                print(f"     - {fk[0]} → {fk[1]}.{fk[2]}")
        print()

def print_sql_statements():
    """In các câu lệnh SQL theo yêu cầu"""
    print("=" * 80)
    print("CÁC CÂU LỆNH SQL THEO YÊU CẦU")
    print("=" * 80)
    print()
    
    # Yêu cầu 1: Thêm nhà cung cấp mới
    print("1️⃣ THÊM NHÀ CUNG CẤP MỚI:")
    print("-" * 40)
    print("-- Thêm một nhà cung cấp mới vào quan hệ NHA_CUNG_CAP")
    print("""INSERT INTO NHA_CUNG_CAP (So_hieu_NCC, Ten_NCC, Dia_chi, So_dien_thoai)
VALUES ('NCC001', 'Công ty TNHH ABC', '123 Đường Lê Lợi, TP.HCM', '0901234567');""")
    print()
    
    print("-- Ví dụ thêm nhiều nhà cung cấp cùng lúc:")
    print("""INSERT INTO NHA_CUNG_CAP (So_hieu_NCC, Ten_NCC, Dia_chi, So_dien_thoai)
VALUES 
    ('NCC002', 'Công ty XYZ', '456 Nguyễn Huệ, Hà Nội', '0987654321'),
    ('NCC003', 'Siêu thị DEF', '789 Trần Hưng Đạo, Đà Nẵng', '0912345678');""")
    print()
    print()
    
    # Yêu cầu 2: Xóa mặt hàng có trọng lượng < 1
    print("2️⃣ XÓA MẶT HÀNG CÓ TRỌNG LƯỢNG < 1:")
    print("-" * 40)
    print("-- Xóa một hoặc nhiều bộ mặt hàng có trọng lượng nhỏ hơn 1")
    print("""DELETE FROM MAT_HANG 
WHERE Trong_luong < 1;""")
    print()
    
    print("-- Nếu cần xóa an toàn (kiểm tra ràng buộc tham chiếu trước):")
    print("""-- Bước 1: Xóa các bản ghi liên quan trong CHI_TIET_CUNG_CAP
DELETE FROM CHI_TIET_CUNG_CAP 
WHERE Ma_hang IN (SELECT Ma_hang FROM MAT_HANG WHERE Trong_luong < 1);

-- Bước 2: Xóa các bản ghi liên quan trong CHI_TIET_HOA_DON
DELETE FROM CHI_TIET_HOA_DON 
WHERE Ma_hang IN (SELECT Ma_hang FROM MAT_HANG WHERE Trong_luong < 1);

-- Bước 3: Xóa các bản ghi liên quan trong BAN_TAI_GIAN_HANG
DELETE FROM BAN_TAI_GIAN_HANG 
WHERE Ma_hang IN (SELECT Ma_hang FROM MAT_HANG WHERE Trong_luong < 1);

-- Bước 4: Xóa mặt hàng
DELETE FROM MAT_HANG 
WHERE Trong_luong < 1;""")
    print()
    print()
    
    # Yêu cầu 3: Cập nhật đơn giá
    print("3️⃣ CẬP NHẬT ĐƠN GIÁ BÁN HÀNG:")
    print("-" * 40)
    print("-- Cập nhật đơn giá bán = 8.500 đồng cho mặt hàng có mã 'MT001'")
    print("""UPDATE CHI_TIET_HOA_DON 
SET Don_gia_ban = 8500 
WHERE Ma_hang = 'MT001';""")
    print()
    
    print("-- Nếu muốn cập nhật đơn giá cung cấp:")
    print("""UPDATE CHI_TIET_CUNG_CAP 
SET Don_gia = 8500 
WHERE Ma_hang = 'MT001';""")
    print()
    
    print("-- Cập nhật có điều kiện bổ sung (ví dụ: chỉ cập nhật hóa đơn trong tháng hiện tại):")
    print("""UPDATE CHI_TIET_HOA_DON 
SET Don_gia_ban = 8500 
WHERE Ma_hang = 'MT001' 
  AND Ma_hoa_don IN (
      SELECT Ma_hoa_don 
      FROM HOA_DON_BAN 
      WHERE MONTH(Ngay_mua) = MONTH(GETDATE()) 
        AND YEAR(Ngay_mua) = YEAR(GETDATE())
  );""")
    print()

def print_create_tables():
    """In câu lệnh CREATE TABLE cho mô hình quan hệ"""
    print("=" * 80)
    print("CÂU LỆNH TẠO BẢNG (CREATE TABLE)")
    print("=" * 80)
    print()
    
    create_statements = [
        """-- Tạo bảng NHA_CUNG_CAP
CREATE TABLE NHA_CUNG_CAP (
    So_hieu_NCC VARCHAR(10) PRIMARY KEY,
    Ten_NCC NVARCHAR(100) NOT NULL,
    Dia_chi NVARCHAR(200),
    So_dien_thoai VARCHAR(15)
);""",
        
        """-- Tạo bảng MAT_HANG
CREATE TABLE MAT_HANG (
    Ma_hang VARCHAR(10) PRIMARY KEY,
    Ten_hang NVARCHAR(100) NOT NULL,
    Mau_sac NVARCHAR(50),
    Trong_luong DECIMAL(10,2),
    Don_vi_tinh NVARCHAR(20),
    So_luong_ton INT DEFAULT 0
);""",
        
        """-- Tạo bảng GIAN_HANG
CREATE TABLE GIAN_HANG (
    Ma_gian_hang VARCHAR(10) PRIMARY KEY,
    Ten_gian_hang NVARCHAR(100) NOT NULL
);""",
        
        """-- Tạo bảng NHAN_VIEN
CREATE TABLE NHAN_VIEN (
    Ma_nhan_vien VARCHAR(10) PRIMARY KEY,
    Ho_va_ten NVARCHAR(100) NOT NULL,
    Luong DECIMAL(12,2)
);""",
        
        """-- Tạo bảng KHACH_HANG
CREATE TABLE KHACH_HANG (
    Ma_khach_hang VARCHAR(10) PRIMARY KEY,
    Ho_va_ten NVARCHAR(100) NOT NULL,
    Dia_chi NVARCHAR(200)
);""",
        
        """-- Tạo bảng THAN_NHAN
CREATE TABLE THAN_NHAN (
    Ma_nhan_vien VARCHAR(10),
    Ma_than_nhan VARCHAR(10),
    Ho_ten NVARCHAR(100) NOT NULL,
    Ngay_sinh DATE,
    Gioi_tinh NVARCHAR(10),
    Quan_he NVARCHAR(20),
    PRIMARY KEY (Ma_nhan_vien, Ma_than_nhan),
    FOREIGN KEY (Ma_nhan_vien) REFERENCES NHAN_VIEN(Ma_nhan_vien)
);""",
        
        """-- Tạo bảng CHI_TIET_CUNG_CAP
CREATE TABLE CHI_TIET_CUNG_CAP (
    So_hieu_NCC VARCHAR(10),
    Ma_hang VARCHAR(10),
    Don_gia DECIMAL(12,2),
    So_luong_cung_cap INT,
    Ngay_cung_cap DATE,
    PRIMARY KEY (So_hieu_NCC, Ma_hang),
    FOREIGN KEY (So_hieu_NCC) REFERENCES NHA_CUNG_CAP(So_hieu_NCC),
    FOREIGN KEY (Ma_hang) REFERENCES MAT_HANG(Ma_hang)
);""",
        
        """-- Tạo bảng HOA_DON_BAN
CREATE TABLE HOA_DON_BAN (
    Ma_hoa_don VARCHAR(15) PRIMARY KEY,
    Ma_khach_hang VARCHAR(10),
    Ngay_mua DATE NOT NULL,
    FOREIGN KEY (Ma_khach_hang) REFERENCES KHACH_HANG(Ma_khach_hang)
);""",
        
        """-- Tạo bảng CHI_TIET_HOA_DON
CREATE TABLE CHI_TIET_HOA_DON (
    Ma_hoa_don VARCHAR(15),
    Ma_hang VARCHAR(10),
    So_luong INT NOT NULL,
    Don_gia_ban DECIMAL(12,2) NOT NULL,
    PRIMARY KEY (Ma_hoa_don, Ma_hang),
    FOREIGN KEY (Ma_hoa_don) REFERENCES HOA_DON_BAN(Ma_hoa_don),
    FOREIGN KEY (Ma_hang) REFERENCES MAT_HANG(Ma_hang)
);""",
        
        """-- Tạo bảng PHAN_CONG_TU_VAN
CREATE TABLE PHAN_CONG_TU_VAN (
    Ma_nhan_vien VARCHAR(10),
    Ma_gian_hang VARCHAR(10),
    Ngay_bat_dau DATE,
    Ngay_ket_thuc DATE,
    PRIMARY KEY (Ma_nhan_vien, Ma_gian_hang),
    FOREIGN KEY (Ma_nhan_vien) REFERENCES NHAN_VIEN(Ma_nhan_vien),
    FOREIGN KEY (Ma_gian_hang) REFERENCES GIAN_HANG(Ma_gian_hang)
);""",
        
        """-- Tạo bảng BAN_TAI_GIAN_HANG
CREATE TABLE BAN_TAI_GIAN_HANG (
    Ma_hang VARCHAR(10) PRIMARY KEY,
    Ma_gian_hang VARCHAR(10) NOT NULL,
    FOREIGN KEY (Ma_hang) REFERENCES MAT_HANG(Ma_hang),
    FOREIGN KEY (Ma_gian_hang) REFERENCES GIAN_HANG(Ma_gian_hang)
);"""
    ]
    
    for statement in create_statements:
        print(statement)
        print()

def create_relational_diagram():
    """Tạo sơ đồ mô hình quan hệ"""
    dot = Digraph(comment='Mô hình Quan hệ - Bài tập 3.3')
    dot.attr(rankdir='TB', size='24,18', dpi='300')
    dot.attr('node', fontname='Arial', fontsize='8')
    dot.attr('edge', fontname='Arial', fontsize='7')

    # === CÁC BẢNG CHÍNH ===
    tables = {
        'NHA_CUNG_CAP': ['So_hieu_NCC (PK)', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'],
        'MAT_HANG': ['Ma_hang (PK)', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'],
        'GIAN_HANG': ['Ma_gian_hang (PK)', 'Ten_gian_hang'],
        'NHAN_VIEN': ['Ma_nhan_vien (PK)', 'Ho_va_ten', 'Luong'],
        'KHACH_HANG': ['Ma_khach_hang (PK)', 'Ho_va_ten', 'Dia_chi'],
        'THAN_NHAN': ['Ma_nhan_vien (PK,FK)', 'Ma_than_nhan (PK)', 'Ho_ten', 'Ngay_sinh', 'Gioi_tinh', 'Quan_he'],
        'CHI_TIET_CUNG_CAP': ['So_hieu_NCC (PK,FK)', 'Ma_hang (PK,FK)', 'Don_gia', 'So_luong_cung_cap', 'Ngay_cung_cap'],
        'HOA_DON_BAN': ['Ma_hoa_don (PK)', 'Ma_khach_hang (FK)', 'Ngay_mua'],
        'CHI_TIET_HOA_DON': ['Ma_hoa_don (PK,FK)', 'Ma_hang (PK,FK)', 'So_luong', 'Don_gia_ban'],
        'PHAN_CONG_TU_VAN': ['Ma_nhan_vien (PK,FK)', 'Ma_gian_hang (PK,FK)', 'Ngay_bat_dau', 'Ngay_ket_thuc'],
        'BAN_TAI_GIAN_HANG': ['Ma_hang (PK,FK)', 'Ma_gian_hang (FK)']
    }

    # Tạo các node bảng
    for table, fields in tables.items():
        label = f"{table}\\n"
        for field in fields:
            if '(PK' in field:
                label += f"<U>{field}</U>\\n"
            elif '(FK)' in field:
                label += f"<I>{field}</I>\\n"
            else:
                label += f"{field}\\n"

        if table in ['CHI_TIET_CUNG_CAP', 'CHI_TIET_HOA_DON', 'PHAN_CONG_TU_VAN', 'BAN_TAI_GIAN_HANG']:
            dot.node(table, label=label, shape='box', style='filled', fillcolor='lightyellow')
        elif table == 'THAN_NHAN':
            dot.node(table, label=label, shape='box', style='filled', fillcolor='lightcyan')
        else:
            dot.node(table, label=label, shape='box', style='filled', fillcolor='lightblue')

    # === CÁC LIÊN KẾT FOREIGN KEY ===
    # Quan hệ bắt buộc (nét liền) - Foreign Key NOT NULL
    mandatory_relationships = [
        ('NHA_CUNG_CAP', 'CHI_TIET_CUNG_CAP', '1:N'),
        ('MAT_HANG', 'CHI_TIET_CUNG_CAP', '1:N'),
        ('MAT_HANG', 'BAN_TAI_GIAN_HANG', '1:1'),
        ('GIAN_HANG', 'BAN_TAI_GIAN_HANG', '1:N'),
        ('NHAN_VIEN', 'PHAN_CONG_TU_VAN', '1:N'),
        ('GIAN_HANG', 'PHAN_CONG_TU_VAN', '1:N'),
        ('HOA_DON_BAN', 'CHI_TIET_HOA_DON', '1:N'),
        ('MAT_HANG', 'CHI_TIET_HOA_DON', '1:N'),
        ('NHAN_VIEN', 'THAN_NHAN', '1:N')
    ]

    # Quan hệ tùy chọn (nét đứt) - Foreign Key có thể NULL
    optional_relationships = [
        ('KHACH_HANG', 'HOA_DON_BAN', '1:N')
    ]

    # Vẽ quan hệ bắt buộc (nét liền)
    for parent, child, card in mandatory_relationships:
        dot.edge(parent, child, label=card, style='solid', color='blue', penwidth='2')

    # Vẽ quan hệ tùy chọn (nét đứt)
    for parent, child, card in optional_relationships:
        dot.edge(parent, child, label=card, style='dashed', color='green', penwidth='2')

    return dot

def create_sql_examples_diagram():
    """Tạo sơ đồ minh họa các câu lệnh SQL"""
    dot = Digraph(comment='Ví dụ SQL - Bài tập 3.3')
    dot.attr(rankdir='LR', size='20,12', dpi='300')
    dot.attr('node', fontname='Arial', fontsize='9')

    # Tạo các node cho 3 yêu cầu
    dot.node('INSERT_NCC',
             'YÊU CẦU 1: THÊM NHÀ CUNG CẤP\\n\\nINSERT INTO NHA_CUNG_CAP\\n(So_hieu_NCC, Ten_NCC, Dia_chi, So_dien_thoai)\\nVALUES\\n(\'NCC001\', \'Công ty ABC\',\\n\'123 Lê Lợi, TP.HCM\', \'0901234567\');',
             shape='box', style='filled', fillcolor='lightgreen')

    dot.node('DELETE_MATHANG',
             'YÊU CẦU 2: XÓA MẶT HÀNG\\n\\nDELETE FROM MAT_HANG\\nWHERE Trong_luong < 1;\\n\\n(Lưu ý: Cần xóa các bản ghi\\nliên quan trước khi xóa mặt hàng)',
             shape='box', style='filled', fillcolor='lightcoral')

    dot.node('UPDATE_DONGIA',
             'YÊU CẦU 3: CẬP NHẬT ĐƠN GIÁ\\n\\nUPDATE CHI_TIET_HOA_DON\\nSET Don_gia_ban = 8500\\nWHERE Ma_hang = \'MT001\';',
             shape='box', style='filled', fillcolor='lightyellow')

    # Tạo các bảng liên quan
    dot.node('NHA_CUNG_CAP_TABLE', 'NHA_CUNG_CAP\\nSo_hieu_NCC\\nTen_NCC\\nDia_chi\\nSo_dien_thoai',
             shape='box', style='filled', fillcolor='lightblue')

    dot.node('MAT_HANG_TABLE', 'MAT_HANG\\nMa_hang\\nTen_hang\\nMau_sac\\nTrong_luong\\nDon_vi_tinh\\nSo_luong_ton',
             shape='box', style='filled', fillcolor='lightblue')

    dot.node('CHI_TIET_HOA_DON_TABLE', 'CHI_TIET_HOA_DON\\nMa_hoa_don\\nMa_hang\\nSo_luong\\nDon_gia_ban',
             shape='box', style='filled', fillcolor='lightyellow')

    # Tạo các liên kết
    dot.edge('INSERT_NCC', 'NHA_CUNG_CAP_TABLE', label='INSERT', color='green')
    dot.edge('DELETE_MATHANG', 'MAT_HANG_TABLE', label='DELETE', color='red')
    dot.edge('UPDATE_DONGIA', 'CHI_TIET_HOA_DON_TABLE', label='UPDATE', color='orange')

    return dot

def main():
    """Chương trình chính"""
    print_relational_model()
    print_sql_statements()
    print_create_tables()

    print("=" * 80)
    print("🎨 ĐANG TẠO CÁC SƠ ĐỒ MINH HỌA...")
    print("=" * 80)

    # Tạo sơ đồ mô hình quan hệ
    print("1. Tạo sơ đồ mô hình quan hệ...")
    relational_diagram = create_relational_diagram()
    relational_diagram.render('bai_3_3_mo_hinh_quan_he', format='png', cleanup=True)
    print("✓ Đã tạo bai_3_3_mo_hinh_quan_he.png")

    # Tạo sơ đồ ví dụ SQL
    print("2. Tạo sơ đồ ví dụ SQL...")
    sql_diagram = create_sql_examples_diagram()
    sql_diagram.render('bai_3_3_vi_du_sql', format='png', cleanup=True)
    print("✓ Đã tạo bai_3_3_vi_du_sql.png")

    print()
    print("=" * 80)
    print("✅ HOÀN THÀNH BÀI TẬP 3.3")
    print("=" * 80)
    print("📝 Tóm tắt:")
    print("• Đã chuyển đổi mô hình E-R thành 11 quan hệ")
    print("• Đã viết câu lệnh INSERT để thêm nhà cung cấp")
    print("• Đã viết câu lệnh DELETE để xóa mặt hàng có trọng lượng < 1")
    print("• Đã viết câu lệnh UPDATE để cập nhật đơn giá mặt hàng MT001")
    print("• Đã cung cấp đầy đủ câu lệnh CREATE TABLE")
    print("• Đã tạo 2 sơ đồ minh họa:")
    print("  - bai_3_3_mo_hinh_quan_he.png (Mô hình quan hệ)")
    print("  - bai_3_3_vi_du_sql.png (Ví dụ SQL)")

if __name__ == "__main__":
    main()
