#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tạo sơ đồ E-R đúng format như ảnh mẫu
Sử dụng Graphviz để vẽ ERD với hình thoi quan hệ và cardinality 1..1, 1..n
"""

from graphviz import Digraph
import os

def create_erd_kinh_dien():
    """Tạo sơ đồ E-R kinh điển với hình thoi quan hệ"""
    dot = Digraph(comment='ERD Kinh Điển - Hệ Thống Siêu Thị')
    dot.attr(rankdir='TB', size='16,12', dpi='300')
    dot.attr('node', fontname='Arial', fontsize='10')
    dot.attr('edge', fontname='Arial', fontsize='9')
    
    # Tạo các thực thể - hộp chữ nhật màu xanh dương
    entities = {
        'KHACH_HANG': ['Mã KH', '<PERSON><PERSON> tê<PERSON>', 'Địa chỉ'],
        'DON_HANG': ['<PERSON><PERSON> HĐ', '<PERSON>ã KH', '<PERSON>ã hàng', '<PERSON><PERSON><PERSON> đặt', '<PERSON><PERSON> lượng'],
        'HANG': ['Mã hàng', 'Tên hàng', 'ĐVT', 'Đơn giá']
    }
    
    for entity, attrs in entities.items():
        label = f"{entity}\\n"
        for i, attr in enumerate(attrs):
            if i == 0:  # Primary key
                label += f"<U>{attr}</U>\\n"
            else:
                label += f"{attr}\\n"
        
        dot.node(entity, label=label, shape='box', style='filled', fillcolor='lightblue')
    
    # Tạo các quan hệ - hình thoi màu xanh lá
    dot.node('dat', label='đặt', shape='diamond', style='filled', fillcolor='lightgreen')
    dot.node('co', label='có', shape='diamond', style='filled', fillcolor='lightgreen')
    
    # Tạo các liên kết với cardinality đúng format
    dot.edge('KHACH_HANG', 'dat', label='1..1')
    dot.edge('dat', 'DON_HANG', label='1..n')
    dot.edge('DON_HANG', 'co', label='1..n')
    dot.edge('co', 'HANG', label='1..1')
    
    return dot

def create_erd_mo_rong():
    """Tạo sơ đồ E-R mở rộng theo ảnh mẫu"""
    dot = Digraph(comment='ERD Mở Rộng - Hệ Thống Siêu Thị')
    dot.attr(rankdir='TB', size='20,16', dpi='300')
    dot.attr('node', fontname='Arial', fontsize='9')
    dot.attr('edge', fontname='Arial', fontsize='8')
    
    # Tạo các thực thể chính
    entities = {
        'NHA_CUNG_CAP': ['So_hieu_NCC', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'],
        'KHACH_HANG': ['Ma_khach_hang', 'Ho_va_ten', 'Dia_chi'],
        'HOA_DON_BAN': ['Ma_hoa_don', 'Ngay_ban_hang', 'So_hieu_NCC'],
        'HOA_DON_MUA': ['Ma_hoa_don', 'Ngay_mua_hang', 'Ma_khach_hang'],
        'MAT_HANG': ['Ma_hang', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'],
        'NHAN_VIEN': ['Ma_nhan_vien', 'Ho_va_ten', 'Luong'],
        'GIAN_HANG': ['Ma_gian_hang', 'Ten_gian_hang'],
        'THAN_NHAN': ['Ma_nhan_vien', 'Ma_than_nhan', 'Ho_ten', 'Ngay_sinh', 'Gioi_tinh']
    }
    
    # Tạo các thực thể yếu (weak entities)
    weak_entities = {
        'CHI_TIET_BAN': ['Ma_hoa_don', 'Ma_hang', 'So_luong', 'Don_gia'],
        'CHI_TIET_MUA': ['Ma_hoa_don', 'Ma_hang', 'So_luong', 'Don_gia']
    }
    
    # Vẽ các thực thể chính
    for entity, attrs in entities.items():
        label = f"{entity}\\n"
        for i, attr in enumerate(attrs):
            if (entity == 'THAN_NHAN' and i < 2) or i == 0:
                label += f"<U>{attr}</U>\\n"
            else:
                label += f"{attr}\\n"
        
        dot.node(entity, label=label, shape='box', style='filled', fillcolor='lightblue')
    
    # Vẽ các thực thể yếu
    for entity, attrs in weak_entities.items():
        label = f"{entity}\\n"
        for i, attr in enumerate(attrs):
            if i < 2:  # Composite key
                label += f"<U>{attr}</U>\\n"
            else:
                label += f"{attr}\\n"
        
        dot.node(entity, label=label, shape='box', style='filled', fillcolor='lightyellow')
    
    # Tạo các quan hệ - hình thoi
    relationships = ['lap', 'mua', 'cung_cap', 'ban_tai', 'tu_van', 'co']
    for rel in relationships:
        dot.node(rel, label=rel, shape='diamond', style='filled', fillcolor='lightgreen')
    
    # Tạo các liên kết
    connections = [
        ('NHA_CUNG_CAP', 'lap', '1..1'),
        ('lap', 'HOA_DON_BAN', '1..n'),
        ('KHACH_HANG', 'mua', '1..1'),
        ('mua', 'HOA_DON_MUA', '1..n'),
        ('HOA_DON_BAN', 'CHI_TIET_BAN', '1..n'),
        ('CHI_TIET_BAN', 'MAT_HANG', '1..1'),
        ('HOA_DON_MUA', 'CHI_TIET_MUA', '1..n'),
        ('CHI_TIET_MUA', 'MAT_HANG', '1..1'),
        ('MAT_HANG', 'ban_tai', '1..n'),
        ('ban_tai', 'GIAN_HANG', '1..1'),
        ('NHAN_VIEN', 'tu_van', '1..n'),
        ('tu_van', 'GIAN_HANG', '1..1'),
        ('NHAN_VIEN', 'co', '1..1'),
        ('co', 'THAN_NHAN', '1..n')
    ]
    
    for source, target, card in connections:
        dot.edge(source, target, label=card)
    
    return dot

def main():
    """Tạo và lưu các sơ đồ ERD"""
    print("Đang tạo sơ đồ E-R kinh điển...")
    erd1 = create_erd_kinh_dien()
    erd1.render('erd_kinh_dien_correct', format='png', cleanup=True)
    print("✓ Đã tạo erd_kinh_dien_correct.png")
    
    print("Đang tạo sơ đồ E-R mở rộng...")
    erd2 = create_erd_mo_rong()
    erd2.render('erd_mo_rong_correct', format='png', cleanup=True)
    print("✓ Đã tạo erd_mo_rong_correct.png")
    
    print("\nHoàn thành! Các file đã được tạo:")
    print("- erd_kinh_dien_correct.png")
    print("- erd_mo_rong_correct.png")

if __name__ == "__main__":
    main()
