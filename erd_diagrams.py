import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# Thiết lập font hỗ trợ tiếng Việt
plt.rcParams['font.family'] = 'DejaVu Sans'

def create_entity_box(ax, x, y, width, height, title, attributes, color='lightblue'):
    """Tạo hộp thực thể với tiêu đề và thuộc tính"""
    # Vẽ hộp chính
    rect = FancyBboxPatch((x, y), width, height, 
                         boxstyle="round,pad=0.02", 
                         facecolor=color, 
                         edgecolor='black', 
                         linewidth=1.5)
    ax.add_patch(rect)
    
    # Vẽ tiêu đề
    ax.text(x + width/2, y + height - 0.3, title, 
            ha='center', va='center', fontweight='bold', fontsize=10)
    
    # Vẽ đường phân cách
    ax.plot([x + 0.1, x + width - 0.1], [y + height - 0.6, y + height - 0.6], 
            'k-', linewidth=1)
    
    # Vẽ các thuộc tính
    for i, attr in enumerate(attributes):
        y_pos = y + height - 1.0 - (i * 0.3)
        if attr.startswith('*'):  # Khóa chính
            ax.text(x + width/2, y_pos, attr[1:], 
                   ha='center', va='center', fontweight='bold', 
                   fontsize=8, style='italic')
        else:
            ax.text(x + width/2, y_pos, attr, 
                   ha='center', va='center', fontsize=8)

def create_relationship_diamond(ax, x, y, width, height, title, color='lightgreen'):
    """Tạo hình thoi quan hệ"""
    # Tạo các điểm của hình thoi
    diamond_x = [x + width/2, x + width, x + width/2, x]
    diamond_y = [y + height, y + height/2, y, y + height/2]
    
    diamond = patches.Polygon(list(zip(diamond_x, diamond_y)), 
                             facecolor=color, edgecolor='black', linewidth=1.5)
    ax.add_patch(diamond)
    
    # Thêm text
    ax.text(x + width/2, y + height/2, title, 
            ha='center', va='center', fontweight='bold', fontsize=9)

def create_weak_entity_box(ax, x, y, width, height, title, attributes):
    """Tạo thực thể yếu với đường viền kép"""
    # Vẽ hộp ngoài
    rect1 = FancyBboxPatch((x-0.05, y-0.05), width+0.1, height+0.1, 
                          boxstyle="round,pad=0.02", 
                          facecolor='none', 
                          edgecolor='black', 
                          linewidth=2)
    ax.add_patch(rect1)
    
    # Vẽ hộp trong
    create_entity_box(ax, x, y, width, height, title, attributes, 'lightyellow')

def draw_line_with_cardinality(ax, start, end, start_card='', end_card=''):
    """Vẽ đường nối với ký hiệu bản số"""
    ax.plot([start[0], end[0]], [start[1], end[1]], 'k-', linewidth=1.5)
    
    # Thêm ký hiệu bản số
    if start_card:
        ax.text(start[0] + 0.2, start[1] + 0.1, start_card, 
               fontsize=8, fontweight='bold')
    if end_card:
        ax.text(end[0] - 0.2, end[1] + 0.1, end_card, 
               fontsize=8, fontweight='bold')

def create_extended_erd():
    """Tạo sơ đồ E-R mở rộng"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 15)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # Tiêu đề
    ax.text(10, 14.5, 'SƠ ĐỒ THỰC THỂ LIÊN KẾT MỞ RỘNG - HỆ THỐNG SIÊU THỊ', 
            ha='center', va='center', fontsize=14, fontweight='bold')
    
    # Thực thể NHÀ CUNG CẤP
    create_entity_box(ax, 1, 11, 3, 2.5, 'NHÀ CUNG CẤP', 
                     ['*Số hiệu NCC', 'Tên NCC', 'Địa chỉ', 'Số điện thoại'])
    
    # Thực thể MẶT HÀNG
    create_entity_box(ax, 8, 11, 3, 2.5, 'MẶT HÀNG', 
                     ['*Mã hàng', 'Tên hàng', 'Màu sắc', 'Trọng lượng', 'Đơn vị tính', 'Số lượng tồn'])
    
    # Quan hệ CUNG CẤP
    create_relationship_diamond(ax, 4.5, 11.5, 3, 1.5, 'CUNG CẤP')
    
    # Thực thể GIAN HÀNG
    create_entity_box(ax, 15, 11, 3, 2, 'GIAN HÀNG', 
                     ['*Mã gian hàng', 'Tên gian hàng'])
    
    # Thực thể NHÂN VIÊN
    create_entity_box(ax, 8, 7.5, 3, 2, 'NHÂN VIÊN', 
                     ['*Mã nhân viên', 'Họ và tên', 'Lương'])
    
    # Thực thể yếu THÂN NHÂN
    create_weak_entity_box(ax, 13, 7.5, 3, 2.5, 'THÂN NHÂN', 
                          ['*Mã thân nhân', 'Họ tên', 'Ngày sinh', 'Giới tính', 'Quan hệ'])
    
    # Thực thể KHÁCH HÀNG
    create_entity_box(ax, 1, 4, 3, 2, 'KHÁCH HÀNG', 
                     ['*Mã khách hàng', 'Họ và tên', 'Địa chỉ'])
    
    # Thực thể HÓA ĐƠN MUA
    create_entity_box(ax, 8, 4, 3, 2, 'HÓA ĐƠN MUA', 
                     ['*Mã hóa đơn', 'Ngày mua hàng'])
    
    # Thực thể HÓA ĐƠN BÁN (từ NCC)
    create_entity_box(ax, 1, 7.5, 3, 2, 'HÓA ĐƠN BÁN', 
                     ['*Mã hóa đơn', 'Ngày bán hàng'])
    
    # Các quan hệ
    create_relationship_diamond(ax, 12, 11.5, 2.5, 1.5, 'BÁN TẠI')
    create_relationship_diamond(ax, 12, 8, 2.5, 1.5, 'TƯ VẤN')
    create_relationship_diamond(ax, 11.5, 7.5, 1.5, 2, 'CÓ', 'lightcoral')
    create_relationship_diamond(ax, 4.5, 4.5, 3, 1.5, 'MUA')
    create_relationship_diamond(ax, 4.5, 8, 3, 1.5, 'LẬP')
    create_relationship_diamond(ax, 12, 4.5, 2.5, 1.5, 'CHI TIẾT')
    
    # Vẽ các đường nối với bản số
    # NCC - CUNG CẤP - MẶT HÀNG
    draw_line_with_cardinality(ax, (4, 12.25), (4.5, 12.25), '1', '')
    draw_line_with_cardinality(ax, (7.5, 12.25), (8, 12.25), '', 'M')
    
    # MẶT HÀNG - BÁN TẠI - GIAN HÀNG
    draw_line_with_cardinality(ax, (11, 12.25), (12, 12.25), 'M', '')
    draw_line_with_cardinality(ax, (14.5, 12.25), (15, 12.25), '', '1')
    
    # NHÂN VIÊN - TƯ VẤN - GIAN HÀNG
    draw_line_with_cardinality(ax, (11, 8.5), (12, 8.75), '1', '')
    draw_line_with_cardinality(ax, (14.5, 8.75), (15, 12), '', 'M')
    
    # NHÂN VIÊN - CÓ - THÂN NHÂN
    draw_line_with_cardinality(ax, (11, 8.5), (11.5, 8.5), '1', '')
    draw_line_with_cardinality(ax, (13, 8.5), (13, 8.5), '', 'M')
    
    # KHÁCH HÀNG - MUA - HÓA ĐƠN MUA
    draw_line_with_cardinality(ax, (4, 5), (4.5, 5.25), '1', '')
    draw_line_with_cardinality(ax, (7.5, 5.25), (8, 5), '', 'M')
    
    # NCC - LẬP - HÓA ĐƠN BÁN
    draw_line_with_cardinality(ax, (2.5, 11), (2.5, 9.5), '1', '')
    draw_line_with_cardinality(ax, (2.5, 9.5), (2.5, 9.5), '', 'M')
    
    # HÓA ĐƠN MUA - CHI TIẾT - MẶT HÀNG
    draw_line_with_cardinality(ax, (11, 5), (12, 5.25), 'M', '')
    draw_line_with_cardinality(ax, (14.5, 5.25), (9.5, 11), '', 'M')
    
    plt.tight_layout()
    return fig

def create_classical_erd():
    """Tạo sơ đồ E-R kinh điển (chuyển đổi từ mở rộng)"""
    fig, ax = plt.subplots(1, 1, figsize=(18, 14))
    ax.set_xlim(0, 22)
    ax.set_ylim(0, 16)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # Tiêu đề
    ax.text(11, 15.5, 'SƠ ĐỒ THỰC THỂ LIÊN KẾT KINH ĐIỂN - HỆ THỐNG SIÊU THỊ', 
            ha='center', va='center', fontsize=14, fontweight='bold')
    
    # Các thực thể chính
    create_entity_box(ax, 1, 12, 3.5, 2.5, 'NHA_CUNG_CAP', 
                     ['*So_hieu_NCC', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'])
    
    create_entity_box(ax, 9, 12, 3.5, 3, 'MAT_HANG', 
                     ['*Ma_hang', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'])
    
    create_entity_box(ax, 17, 12, 3.5, 2, 'GIAN_HANG', 
                     ['*Ma_gian_hang', 'Ten_gian_hang'])
    
    create_entity_box(ax, 9, 8, 3.5, 2, 'NHAN_VIEN', 
                     ['*Ma_nhan_vien', 'Ho_va_ten', 'Luong'])
    
    create_entity_box(ax, 1, 8, 3.5, 2, 'KHACH_HANG', 
                     ['*Ma_khach_hang', 'Ho_va_ten', 'Dia_chi'])
    
    create_entity_box(ax, 1, 4, 3.5, 2, 'HOA_DON_BAN', 
                     ['*Ma_hoa_don', 'Ngay_ban_hang', '*So_hieu_NCC'])
    
    create_entity_box(ax, 9, 4, 3.5, 2, 'HOA_DON_MUA', 
                     ['*Ma_hoa_don', 'Ngay_mua_hang', '*Ma_khach_hang'])
    
    create_entity_box(ax, 17, 8, 3.5, 2.5, 'THAN_NHAN', 
                     ['*Ma_nhan_vien', '*Ma_than_nhan', 'Ho_ten', 'Ngay_sinh', 'Gioi_tinh', 'Quan_he'])
    
    # Các bảng quan hệ (từ quan hệ nhiều-nhiều)
    create_entity_box(ax, 5.5, 12, 3, 2.5, 'CUNG_CAP', 
                     ['*So_hieu_NCC', '*Ma_hang', 'Don_gia'], 'lightgreen')
    
    create_entity_box(ax, 13.5, 12, 3, 2, 'BAN_TAI', 
                     ['*Ma_hang', '*Ma_gian_hang'], 'lightgreen')
    
    create_entity_box(ax, 13.5, 8, 3, 2, 'TU_VAN', 
                     ['*Ma_nhan_vien', '*Ma_gian_hang'], 'lightgreen')
    
    create_entity_box(ax, 13.5, 4, 3, 2.5, 'CHI_TIET_MUA', 
                     ['*Ma_hoa_don', '*Ma_hang', 'So_luong', 'Don_gia'], 'lightgreen')
    
    create_entity_box(ax, 5.5, 4, 3, 2.5, 'CHI_TIET_BAN', 
                     ['*Ma_hoa_don', '*Ma_hang', 'So_luong'], 'lightgreen')
    
    # Vẽ các đường nối (quan hệ 1-nhiều)
    # NHA_CUNG_CAP -> CUNG_CAP
    ax.plot([4.5, 5.5], [13.25, 13.25], 'k-', linewidth=1.5)
    ax.text(4.7, 13.4, '1', fontsize=8, fontweight='bold')
    ax.text(5.3, 13.4, 'M', fontsize=8, fontweight='bold')
    
    # MAT_HANG -> CUNG_CAP
    ax.plot([9, 8.5], [13.5, 13.5], 'k-', linewidth=1.5)
    ax.text(8.7, 13.6, 'M', fontsize=8, fontweight='bold')
    ax.text(8.8, 13.6, '1', fontsize=8, fontweight='bold')
    
    # MAT_HANG -> BAN_TAI
    ax.plot([12.5, 13.5], [13.5, 13], 'k-', linewidth=1.5)
    ax.text(12.7, 13.3, '1', fontsize=8, fontweight='bold')
    ax.text(13.3, 13.1, 'M', fontsize=8, fontweight='bold')
    
    # GIAN_HANG -> BAN_TAI
    ax.plot([17, 16.5], [13, 13], 'k-', linewidth=1.5)
    ax.text(16.7, 13.1, '1', fontsize=8, fontweight='bold')
    ax.text(16.3, 13.1, 'M', fontsize=8, fontweight='bold')
    
    # NHAN_VIEN -> TU_VAN
    ax.plot([12.5, 13.5], [9, 9], 'k-', linewidth=1.5)
    ax.text(12.7, 9.1, '1', fontsize=8, fontweight='bold')
    ax.text(13.3, 9.1, 'M', fontsize=8, fontweight='bold')
    
    # GIAN_HANG -> TU_VAN
    ax.plot([17, 16.5], [10, 9], 'k-', linewidth=1.5)
    ax.text(16.7, 9.5, 'M', fontsize=8, fontweight='bold')
    ax.text(16.3, 9.1, '1', fontsize=8, fontweight='bold')
    
    # NHAN_VIEN -> THAN_NHAN
    ax.plot([12.5, 17], [9, 9.25], 'k-', linewidth=1.5)
    ax.text(14, 9.1, '1', fontsize=8, fontweight='bold')
    ax.text(16.8, 9.3, 'M', fontsize=8, fontweight='bold')
    
    # KHACH_HANG -> HOA_DON_MUA
    ax.plot([4.5, 9], [9, 5], 'k-', linewidth=1.5)
    ax.text(6, 7, '1', fontsize=8, fontweight='bold')
    ax.text(8.8, 5.1, 'M', fontsize=8, fontweight='bold')
    
    # NHA_CUNG_CAP -> HOA_DON_BAN
    ax.plot([2.75, 2.75], [12, 6], 'k-', linewidth=1.5)
    ax.text(2.9, 9, '1', fontsize=8, fontweight='bold')
    ax.text(2.9, 6.1, 'M', fontsize=8, fontweight='bold')
    
    # HOA_DON_MUA -> CHI_TIET_MUA
    ax.plot([12.5, 13.5], [5, 5.25], 'k-', linewidth=1.5)
    ax.text(12.7, 5.1, '1', fontsize=8, fontweight='bold')
    ax.text(13.3, 5.3, 'M', fontsize=8, fontweight='bold')
    
    # MAT_HANG -> CHI_TIET_MUA
    ax.plot([10.75, 15], [12, 6.5], 'k-', linewidth=1.5)
    ax.text(12, 9, 'M', fontsize=8, fontweight='bold')
    ax.text(14.8, 6.6, '1', fontsize=8, fontweight='bold')
    
    # HOA_DON_BAN -> CHI_TIET_BAN
    ax.plot([4.5, 5.5], [5, 5.25], 'k-', linewidth=1.5)
    ax.text(4.7, 5.1, '1', fontsize=8, fontweight='bold')
    ax.text(5.3, 5.3, 'M', fontsize=8, fontweight='bold')
    
    # MAT_HANG -> CHI_TIET_BAN
    ax.plot([9, 8.5], [12, 6.5], 'k-', linewidth=1.5)
    ax.text(8.7, 9, 'M', fontsize=8, fontweight='bold')
    ax.text(8.3, 6.6, '1', fontsize=8, fontweight='bold')
    
    plt.tight_layout()
    return fig

if __name__ == "__main__":
    # Tạo và hiển thị sơ đồ mở rộng
    fig1 = create_extended_erd()
    plt.figure(fig1.number)
    plt.savefig('erd_mo_rong.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Tạo và hiển thị sơ đồ kinh điển
    fig2 = create_classical_erd()
    plt.figure(fig2.number)
    plt.savefig('erd_kinh_dien.png', dpi=300, bbox_inches='tight')
    plt.show()
