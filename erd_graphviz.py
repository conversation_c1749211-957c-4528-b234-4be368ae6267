import graphviz
from graphviz import Digraph

def create_extended_erd():
    """T<PERSON><PERSON> sơ đồ E-R mở rộng bằng Graphviz"""
    dot = Digraph(comment='ERD Mở Rộng - Hệ Thống Siêu Thị')
    dot.attr(rankdir='TB', size='16,12', dpi='300')
    dot.attr('node', shape='box', style='filled', fontname='Arial', fontsize='10')
    dot.attr('edge', fontname='Arial', fontsize='10')
    
    # Định nghĩa các thực thể
    entities = {
        'NHA_CUNG_CAP': ['So_hieu_NCC', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'],
        'MAT_HANG': ['Ma_hang', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'],
        'GIAN_HANG': ['Ma_gian_hang', 'Ten_gian_hang'],
        '<PERSON><PERSON>_VIEN': ['Ma_nhan_vien', 'Ho_va_ten', '<PERSON><PERSON>'],
        'THAN_NHAN': ['Ma_than_nhan', 'Ho_ten', '<PERSON>ay_sinh', 'Gioi_tinh', 'Quan_he'],
        'KHACH_HANG': ['Ma_khach_hang', 'Ho_va_ten', 'Dia_chi'],
        'HOA_DON_BAN': ['Ma_hoa_don', 'Ngay_ban_hang'],
        'HOA_DON_MUA': ['Ma_hoa_don', 'Ngay_mua_hang']
    }
    
    # Tạo các node thực thể
    for entity, attrs in entities.items():
        label = f"{entity}\\n"
        for i, attr in enumerate(attrs):
            if i == 0:  # Khóa chính
                label += f"<U>{attr}</U>\\n"
            else:
                label += f"{attr}\\n"
        dot.node(entity, label=label, fillcolor='lightblue')
    
    # Tạo các node quan hệ
    relationships = ['CUNG_CAP', 'BAN_TAI', 'TU_VAN', 'CO', 'MUA', 'LAP', 'CHI_TIET']
    for rel in relationships:
        dot.node(rel, label=rel, shape='box', fillcolor='lightgreen')
    
    # Định nghĩa các liên kết với cardinality
    connections = [
        ('NHA_CUNG_CAP', 'CUNG_CAP', '1'),
        ('CUNG_CAP', 'MAT_HANG', 'M'),
        ('MAT_HANG', 'BAN_TAI', 'M'),
        ('BAN_TAI', 'GIAN_HANG', '1'),
        ('NHAN_VIEN', 'TU_VAN', 'M'),
        ('TU_VAN', 'GIAN_HANG', '1'),
        ('NHAN_VIEN', 'CO', '1'),
        ('CO', 'THAN_NHAN', 'M'),
        ('KHACH_HANG', 'MUA', '1'),
        ('MUA', 'HOA_DON_MUA', 'M'),
        ('NHA_CUNG_CAP', 'LAP', '1'),
        ('LAP', 'HOA_DON_BAN', 'M'),
        ('HOA_DON_MUA', 'CHI_TIET', '1'),
        ('CHI_TIET', 'MAT_HANG', 'M')
    ]
    
    # Tạo các edge
    for source, target, card in connections:
        dot.edge(source, target, label=card, fontweight='bold')
    
    return dot

def create_classical_erd():
    """Tạo sơ đồ E-R kinh điển bằng Graphviz"""
    dot = Digraph(comment='ERD Kinh Điển - Hệ Thống Siêu Thị')
    dot.attr(rankdir='TB', size='18,14', dpi='300')
    dot.attr('node', shape='box', style='filled', fontname='Arial', fontsize='10')
    dot.attr('edge', fontname='Arial', fontsize='10')
    
    # Định nghĩa các thực thể với thuộc tính đầy đủ
    entities = {
        'NHA_CUNG_CAP': ['So_hieu_NCC', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'],
        'MAT_HANG': ['Ma_hang', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'],
        'GIAN_HANG': ['Ma_gian_hang', 'Ten_gian_hang'],
        'NHAN_VIEN': ['Ma_nhan_vien', 'Ho_va_ten', 'Luong'],
        'THAN_NHAN': ['Ma_nhan_vien', 'Ma_than_nhan', 'Ho_ten', 'Ngay_sinh', 'Gioi_tinh'],
        'KHACH_HANG': ['Ma_khach_hang', 'Ho_va_ten', 'Dia_chi'],
        'HOA_DON_BAN': ['Ma_hoa_don', 'Ngay_ban_hang', 'So_hieu_NCC'],
        'HOA_DON_MUA': ['Ma_hoa_don', 'Ngay_mua_hang', 'Ma_khach_hang'],
        'CHI_TIET_BAN': ['Ma_hoa_don', 'Ma_hang', 'So_luong', 'Don_gia'],
        'CHI_TIET_MUA': ['Ma_hoa_don', 'Ma_hang', 'So_luong', 'Don_gia']
    }
    
    # Tạo các node thực thể
    for entity, attrs in entities.items():
        label = f"{entity}\\n"
        for i, attr in enumerate(attrs):
            if i < 2 and entity in ['THAN_NHAN', 'CHI_TIET_BAN', 'CHI_TIET_MUA']:  # Khóa chính kép
                label += f"<U>{attr}</U>\\n"
            elif i == 0:  # Khóa chính đơn
                label += f"<U>{attr}</U>\\n"
            else:
                label += f"{attr}\\n"
        
        # Màu sắc khác nhau cho các loại thực thể
        if entity.startswith('CHI_TIET'):
            dot.node(entity, label=label, fillcolor='lightyellow')
        else:
            dot.node(entity, label=label, fillcolor='lightblue')
    
    # Định nghĩa các liên kết trực tiếp với cardinality
    connections = [
        ('NHA_CUNG_CAP', 'MAT_HANG', '1', 'M', 'cung cấp'),
        ('MAT_HANG', 'GIAN_HANG', 'M', '1', 'bán tại'),
        ('NHAN_VIEN', 'GIAN_HANG', 'M', '1', 'tư vấn'),
        ('NHAN_VIEN', 'THAN_NHAN', '1', 'M', 'có'),
        ('KHACH_HANG', 'HOA_DON_MUA', '1', 'M', 'mua'),
        ('NHA_CUNG_CAP', 'HOA_DON_BAN', '1', 'M', 'lập'),
        ('HOA_DON_BAN', 'CHI_TIET_BAN', '1', 'M', ''),
        ('CHI_TIET_BAN', 'MAT_HANG', 'M', '1', ''),
        ('HOA_DON_MUA', 'CHI_TIET_MUA', '1', 'M', ''),
        ('CHI_TIET_MUA', 'MAT_HANG', 'M', '1', '')
    ]
    
    # Tạo các edge với cardinality
    for source, target, card1, card2, label in connections:
        if label:
            dot.edge(source, target, label=f"{card1}    {label}    {card2}", fontweight='bold')
        else:
            dot.edge(source, target, label=f"{card1}        {card2}", fontweight='bold')
    
    return dot

def create_simple_erd():
    """Tạo sơ đồ E-R đơn giản theo format ảnh mẫu"""
    dot = Digraph(comment='ERD Đơn Giản - Hệ Thống Siêu Thị')
    dot.attr(rankdir='LR', size='16,10', dpi='300')
    dot.attr('node', shape='record', style='filled', fontname='Arial', fontsize='9')
    dot.attr('edge', fontname='Arial', fontsize='10', fontweight='bold')
    
    # Tạo các thực thể với format record
    dot.node('KHACH_HANG', 
             label='{KHACH_HANG|{<pk>Mã KH|Họ tên KH|Địa chỉ}}',
             fillcolor='lightblue')
    
    dot.node('DON_HANG',
             label='{ĐƠN HÀNG|{<pk>Số HĐ|Mã KH|Mã hàng|Ngày đặt|Số lượng}}',
             fillcolor='lightblue')
    
    dot.node('HANG',
             label='{HÀNG|{<pk>Mã hàng|Tên hàng|ĐVT|Đơn giá}}',
             fillcolor='lightblue')
    
    # Tạo các liên kết
    dot.edge('KHACH_HANG:pk', 'DON_HANG', label='1', headlabel='n')
    dot.edge('DON_HANG', 'HANG:pk', label='n', headlabel='1')
    
    return dot

if __name__ == "__main__":
    # Tạo sơ đồ mở rộng
    print("Đang tạo sơ đồ E-R mở rộng...")
    erd_extended = create_extended_erd()
    erd_extended.render('erd_mo_rong_sieu_thi', format='png', cleanup=True)
    
    # Tạo sơ đồ kinh điển
    print("Đang tạo sơ đồ E-R kinh điển...")
    erd_classical = create_classical_erd()
    erd_classical.render('erd_kinh_dien_sieu_thi', format='png', cleanup=True)
    
    # Tạo sơ đồ đơn giản
    print("Đang tạo sơ đồ E-R đơn giản...")
    erd_simple = create_simple_erd()
    erd_simple.render('erd_don_gian_sieu_thi', format='png', cleanup=True)
    
    print("Hoàn thành! Các file PNG đã được tạo.")
