import matplotlib.pyplot as plt
import matplotlib.patches as patches

# Thi<PERSON>t lập font
plt.rcParams['font.family'] = 'Arial'

def create_entity_box(ax, x, y, width, height, title, attributes, color='lightblue'):
    """Tạo hộp thực thể theo format chuẩn"""
    # Vẽ hộp chính
    rect = patches.Rectangle((x, y), width, height, 
                           linewidth=1, edgecolor='black', facecolor=color)
    ax.add_patch(rect)
    
    # Vẽ tiêu đề
    ax.text(x + width/2, y + height - 0.3, title, 
            ha='center', va='center', fontweight='bold', fontsize=9)
    
    # Vẽ đường phân cách
    ax.plot([x, x + width], [y + height - 0.6, y + height - 0.6], 
            'k-', linewidth=1)
    
    # Vẽ các thuộc tính
    for i, attr in enumerate(attributes):
        y_pos = y + height - 1.0 - (i * 0.3)
        if attr.startswith('*'):  # Kh<PERSON>a chính - gạch dưới
            attr_text = attr[1:]
            ax.text(x + width/2, y_pos, attr_text, 
                   ha='center', va='center', fontsize=8)
            # Vẽ gạch dưới cho khóa chính
            text_width = len(attr_text) * 0.05
            ax.plot([x + width/2 - text_width, x + width/2 + text_width], 
                   [y_pos - 0.08, y_pos - 0.08], 'k-', linewidth=1)
        else:
            ax.text(x + width/2, y_pos, attr, 
                   ha='center', va='center', fontsize=8)

def create_relationship_box(ax, x, y, width, height, title):
    """Tạo hộp quan hệ màu xanh lá"""
    rect = patches.Rectangle((x, y), width, height, 
                           linewidth=1, edgecolor='black', facecolor='lightgreen')
    ax.add_patch(rect)
    
    ax.text(x + width/2, y + height/2, title, 
            ha='center', va='center', fontweight='bold', fontsize=9)

def draw_line(ax, x1, y1, x2, y2):
    """Vẽ đường thẳng"""
    ax.plot([x1, x2], [y1, y2], 'k-', linewidth=1)

def add_cardinality(ax, x, y, text):
    """Thêm ký hiệu bản số"""
    ax.text(x, y, text, ha='center', va='center', fontsize=10, fontweight='bold')

def create_extended_erd():
    """Tạo sơ đồ E-R mở rộng"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Tiêu đề
    ax.text(7, 9.5, 'SƠ ĐỒ THỰC THỂ LIÊN KẾT MỞ RỘNG - HỆ THỐNG SIÊU THỊ', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Thực thể chính
    create_entity_box(ax, 1, 6, 2.5, 2, 'NHA_CUNG_CAP', 
                     ['*So_hieu_NCC', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'])
    
    create_entity_box(ax, 6, 6, 2.5, 2.5, 'MAT_HANG', 
                     ['*Ma_hang', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'])
    
    create_entity_box(ax, 10.5, 6, 2.5, 1.5, 'GIAN_HANG', 
                     ['*Ma_gian_hang', 'Ten_gian_hang'])
    
    create_entity_box(ax, 6, 3, 2.5, 1.5, 'NHAN_VIEN', 
                     ['*Ma_nhan_vien', 'Ho_va_ten', 'Luong'])
    
    create_entity_box(ax, 10.5, 3, 2.5, 2, 'THAN_NHAN', 
                     ['*Ma_than_nhan', 'Ho_ten', 'Ngay_sinh', 'Gioi_tinh', 'Quan_he'])
    
    create_entity_box(ax, 1, 3, 2.5, 1.5, 'KHACH_HANG', 
                     ['*Ma_khach_hang', 'Ho_va_ten', 'Dia_chi'])
    
    create_entity_box(ax, 1, 0.5, 2.5, 1.5, 'HOA_DON_BAN', 
                     ['*Ma_hoa_don', 'Ngay_ban_hang'])
    
    create_entity_box(ax, 6, 0.5, 2.5, 1.5, 'HOA_DON_MUA', 
                     ['*Ma_hoa_don', 'Ngay_mua_hang'])
    
    # Quan hệ
    create_relationship_box(ax, 4, 6.5, 1.5, 1, 'CUNG_CAP')
    create_relationship_box(ax, 9, 6.5, 1.2, 1, 'BAN_TAI')
    create_relationship_box(ax, 9, 3.5, 1.2, 1, 'TU_VAN')
    create_relationship_box(ax, 9, 2.5, 1.2, 1, 'CO')
    create_relationship_box(ax, 4, 3.5, 1.5, 1, 'MUA')
    create_relationship_box(ax, 4, 1, 1.5, 1, 'LAP')
    create_relationship_box(ax, 9, 1, 1.2, 1, 'CHI_TIET')
    
    # Vẽ các đường nối
    # NHA_CUNG_CAP - CUNG_CAP
    draw_line(ax, 3.5, 7, 4, 7)
    add_cardinality(ax, 3.7, 7.2, '1')
    
    # CUNG_CAP - MAT_HANG
    draw_line(ax, 5.5, 7, 6, 7)
    add_cardinality(ax, 5.7, 7.2, 'M')
    
    # MAT_HANG - BAN_TAI
    draw_line(ax, 8.5, 7, 9, 7)
    add_cardinality(ax, 8.7, 7.2, 'M')
    
    # BAN_TAI - GIAN_HANG
    draw_line(ax, 10.2, 7, 10.5, 7)
    add_cardinality(ax, 10.3, 7.2, '1')
    
    # NHAN_VIEN - TU_VAN
    draw_line(ax, 8.5, 4, 9, 4)
    add_cardinality(ax, 8.7, 4.2, 'M')
    
    # TU_VAN - GIAN_HANG
    draw_line(ax, 10.2, 4, 11.75, 6)
    add_cardinality(ax, 10.3, 4.2, '1')
    
    # NHAN_VIEN - CO
    draw_line(ax, 8.5, 3.5, 9, 3)
    add_cardinality(ax, 8.7, 3.7, '1')
    
    # CO - THAN_NHAN
    draw_line(ax, 10.2, 3, 10.5, 3.5)
    add_cardinality(ax, 10.3, 3.2, 'M')
    
    # KHACH_HANG - MUA
    draw_line(ax, 3.5, 4, 4, 4)
    add_cardinality(ax, 3.7, 4.2, '1')
    
    # MUA - HOA_DON_MUA
    draw_line(ax, 5.5, 4, 7.25, 2)
    add_cardinality(ax, 5.7, 4.2, 'M')
    
    # NHA_CUNG_CAP - LAP
    draw_line(ax, 2.25, 6, 2.25, 3)
    draw_line(ax, 2.25, 3, 4, 1.5)
    add_cardinality(ax, 2.4, 4.5, '1')
    
    # LAP - HOA_DON_BAN
    draw_line(ax, 4, 1.5, 3.5, 1.25)
    add_cardinality(ax, 3.7, 1.7, 'M')
    
    # HOA_DON_MUA - CHI_TIET
    draw_line(ax, 8.5, 1.25, 9, 1.5)
    add_cardinality(ax, 8.7, 1.5, '1')
    
    # CHI_TIET - MAT_HANG
    draw_line(ax, 10.2, 1.5, 7.25, 6)
    add_cardinality(ax, 10.3, 1.7, 'M')
    
    plt.tight_layout()
    return fig

def create_classical_erd():
    """Tạo sơ đồ E-R kinh điển"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # Tiêu đề
    ax.text(8, 11.5, 'SƠ ĐỒ THỰC THỂ LIÊN KẾT KINH ĐIỂN - HỆ THỐNG SIÊU THỊ', 
            ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Hàng trên
    create_entity_box(ax, 0.5, 8.5, 2.5, 2, 'NHA_CUNG_CAP', 
                     ['*So_hieu_NCC', 'Ten_NCC', 'Dia_chi', 'So_dien_thoai'])
    
    create_relationship_box(ax, 3.5, 9, 1.5, 1, 'CUNG_CAP')
    
    create_entity_box(ax, 5.5, 8, 2.5, 3, 'MAT_HANG', 
                     ['*Ma_hang', 'Ten_hang', 'Mau_sac', 'Trong_luong', 'Don_vi_tinh', 'So_luong_ton'])
    
    create_relationship_box(ax, 8.5, 9, 1.5, 1, 'BAN_TAI')
    
    create_entity_box(ax, 10.5, 8.5, 2.5, 2, 'GIAN_HANG', 
                     ['*Ma_gian_hang', 'Ten_gian_hang'])
    
    # Hàng giữa
    create_entity_box(ax, 5.5, 5, 2.5, 2, 'NHAN_VIEN', 
                     ['*Ma_nhan_vien', 'Ho_va_ten', 'Luong'])
    
    create_relationship_box(ax, 8.5, 5.5, 1.5, 1, 'TU_VAN')
    
    create_entity_box(ax, 13, 5, 2.5, 2.5, 'THAN_NHAN', 
                     ['*Ma_nhan_vien', '*Ma_than_nhan', 'Ho_ten', 'Ngay_sinh', 'Gioi_tinh'])
    
    # Hàng dưới
    create_entity_box(ax, 0.5, 2, 2.5, 2, 'KHACH_HANG', 
                     ['*Ma_khach_hang', 'Ho_va_ten', 'Dia_chi'])
    
    create_entity_box(ax, 0.5, 0, 2.5, 1.5, 'HOA_DON_BAN', 
                     ['*Ma_hoa_don', 'Ngay_ban_hang', '*So_hieu_NCC'])
    
    create_relationship_box(ax, 3.5, 2.5, 1.5, 1, 'CHI_TIET_BAN')
    
    create_entity_box(ax, 5.5, 2, 2.5, 2, 'HOA_DON_MUA', 
                     ['*Ma_hoa_don', 'Ngay_mua_hang', '*Ma_khach_hang'])
    
    create_relationship_box(ax, 8.5, 2.5, 1.5, 1, 'CHI_TIET_MUA')
    
    # Vẽ các đường nối với ký hiệu 1 và M
    # NHA_CUNG_CAP - CUNG_CAP
    draw_line(ax, 3, 9.5, 3.5, 9.5)
    add_cardinality(ax, 3.2, 9.7, '1')
    
    # CUNG_CAP - MAT_HANG  
    draw_line(ax, 5, 9.5, 5.5, 9.5)
    add_cardinality(ax, 5.2, 9.7, 'M')
    
    # MAT_HANG - BAN_TAI
    draw_line(ax, 8, 9.5, 8.5, 9.5)
    add_cardinality(ax, 8.2, 9.7, 'M')
    
    # BAN_TAI - GIAN_HANG
    draw_line(ax, 10, 9.5, 10.5, 9.5)
    add_cardinality(ax, 10.2, 9.7, '1')
    
    # NHAN_VIEN - TU_VAN
    draw_line(ax, 8, 6, 8.5, 6)
    add_cardinality(ax, 8.2, 6.2, 'M')
    
    # TU_VAN - GIAN_HANG
    draw_line(ax, 10, 6, 11.75, 8.5)
    add_cardinality(ax, 10.2, 6.2, '1')
    
    # NHAN_VIEN - THAN_NHAN
    draw_line(ax, 8, 6, 13, 6.25)
    add_cardinality(ax, 10, 6.2, '1')
    add_cardinality(ax, 12.8, 6.4, 'M')
    
    # KHACH_HANG - HOA_DON_MUA
    draw_line(ax, 3, 3, 5.5, 3)
    add_cardinality(ax, 4, 3.2, '1')
    add_cardinality(ax, 5.3, 3.2, 'M')
    
    # NHA_CUNG_CAP - HOA_DON_BAN
    draw_line(ax, 1.75, 8.5, 1.75, 1.5)
    add_cardinality(ax, 1.9, 5, '1')
    add_cardinality(ax, 1.9, 1.7, 'M')
    
    # HOA_DON_BAN - CHI_TIET_BAN
    draw_line(ax, 3, 0.75, 3.5, 3)
    add_cardinality(ax, 3.2, 1.5, '1')
    add_cardinality(ax, 3.2, 2.8, 'M')
    
    # CHI_TIET_BAN - MAT_HANG
    draw_line(ax, 5, 3, 6.75, 8)
    add_cardinality(ax, 5.2, 3.2, 'M')
    add_cardinality(ax, 6.5, 7.8, '1')
    
    # HOA_DON_MUA - CHI_TIET_MUA
    draw_line(ax, 8, 3, 8.5, 3)
    add_cardinality(ax, 8.2, 3.2, '1')
    add_cardinality(ax, 8.3, 3.2, 'M')
    
    # CHI_TIET_MUA - MAT_HANG
    draw_line(ax, 10, 3, 6.75, 8)
    add_cardinality(ax, 9.8, 3.2, 'M')
    add_cardinality(ax, 7, 7.8, '1')
    
    plt.tight_layout()
    return fig

if __name__ == "__main__":
    # Tạo sơ đồ mở rộng
    fig1 = create_extended_erd()
    plt.figure(fig1.number)
    plt.savefig('erd_mo_rong_sieu_thi.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Tạo sơ đồ kinh điển
    fig2 = create_classical_erd()
    plt.figure(fig2.number)
    plt.savefig('erd_kinh_dien_sieu_thi.png', dpi=300, bbox_inches='tight')
    plt.show()
